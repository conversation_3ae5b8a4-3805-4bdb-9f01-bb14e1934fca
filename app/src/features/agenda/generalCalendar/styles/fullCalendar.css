.fc-day {
    color: hsl(200, 100%, 23%);
    font-family: "Cairo";
}

.fc .fc-col-header-cell-cushion {
    text-transform: capitalize;
    font-family: "Cairo";
    color: hsla(200, 100%, 23%, 1);
}

.fc-toolbar-title {
    text-transform: capitalize;
}


/* Only apply fixed height to month view (dayGrid), not week/day views */
.fc-dayGridMonth-view .fc-daygrid-day-frame {
    min-height: 125px;
    /* Minimum height for each day cell - increased for +n button */
    height: 125px !important;
    /* Fixed height to prevent row expansion */
    max-height: 125px !important;
    overflow: visible;
    /* Allow +n events link to show */
}

.fc-day-other {
    opacity: 1;
    background-color: #ffffff;
}

.fc-day-other .fc-daygrid-day-number {
    color: hsl(200, 100%, 23%);
    font-weight: 500;
    font-size: 1em;
}

.fc-day {
    display: table-cell !important;
    visibility: visible !important;
}

.fc-daygrid-day-number {
    display: block !important;
    visibility: visible !important;
}



.fc-view-harness {
    height: auto !important;
}

/* Only apply fixed height to month view week rows */
.fc-dayGridMonth-view .fc-daygrid-week {
    min-height: 125px;
    height: 125px !important;
    /* Fixed height for week rows */
    max-height: 125px !important;
    /* Prevent expansion */
}

/* Ensure event containers within day cells respect fixed height - month view only */
.fc-dayGridMonth-view .fc-daygrid-day-events {
    height: auto !important;
    /* Let FullCalendar manage height for proper dayMaxEvents behavior */
    max-height: 95px !important;
    /* Leave space for day number */
    overflow: visible !important;
    /* Allow +n events link to show */
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
}

/* Ensure day cells maintain fixed height - month view only */
.fc-dayGridMonth-view .fc-daygrid-day {
    height: 125px !important;
    max-height: 125px !important;
    overflow: visible !important;
    /* Allow +n more link to show */
    position: relative !important;
}

/* Fix table row height in month view only */
.fc-dayGridMonth-view .fc-daygrid-body tr {
    height: 125px !important;
    max-height: 125px !important;
}

/* Ensure table cells maintain fixed height - month view only */
.fc-dayGridMonth-view .fc-daygrid-body td {
    height: 125px !important;
    max-height: 125px !important;
    vertical-align: top !important;
    overflow: visible !important;
    /* Allow +n more link to show */
    position: relative !important;
    border-right: 1px solid #ddd !important;
}

.fc-day:not(.fc-day-other) {
    background-color: #ffffff;
    opacity: 1;
}

.fc-day:not(.fc-day-other) .fc-daygrid-day-number {
    color: hsl(200, 100%, 23%);
    font-weight: 500;
}


.fc-day-today {
    background-color: #ffffff !important;
}

.fc-day-today .fc-daygrid-day-number {
    background-color: #005075 !important;
    color: #ffffff !important;
    font-weight: bold !important;
    border-radius: 50% !important;
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 2px !important;
}

.fc-col-header-cell {
    background-color: #f5f5f5;
    border-bottom: 2px solid #dee2e6;
}



.fc-col-header-cell-cushion {
    padding: 8px 4px;
    font-weight: 600;
    text-transform: capitalize;
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.2;
}

/* Fix overlapping text issue in week view headers */
.fc-timeGridWeek-view .fc-col-header-cell-cushion {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    gap: 2px !important;
    padding: 8px 4px !important;
    position: relative !important;
}

.fc-timeGridWeek-view .fc-col-header-cell-cushion div:first-child {
    font-size: 1.1em !important;
    font-weight: bold !important;
    line-height: 1 !important;
    margin: 0 !important;
}

.fc-timeGridWeek-view .fc-col-header-cell-cushion div:last-child {
    font-size: 0.9em !important;
    font-weight: normal !important;
    text-transform: capitalize !important;
    line-height: 1 !important;
    margin: 0 !important;
}

/* Hide default FullCalendar header content in week view to prevent overlap */
.fc-timeGridWeek-view .fc-col-header-cell-cushion .fc-col-header-cell-text {
    display: none !important;
}

/* Ensure no other default content interferes */
.fc-timeGridWeek-view .fc-col-header-cell-cushion>*:not(div) {
    display: none !important;
}

/* Make sure the header cell has proper height */
.fc-timeGridWeek-view .fc-col-header-cell {
    height: auto !important;
    min-height: 60px !important;
}

/* Ensure full week shows all 7 columns properly */
.fc-timeGridWeek-view .fc-scrollgrid {
    width: 100% !important;
}

.fc-timeGridWeek-view .fc-col-header-cell {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

.fc-timeGridWeek-view .fc-timegrid-col {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

/* Work Week Mode Styles (when weekends are hidden) - Layout */
.fc-timeGridWeek-view:not(.fc-weekends) .fc-col-header-cell {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

.fc-timeGridWeek-view:not(.fc-weekends) .fc-timegrid-col {
    width: auto !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
}

.fc-timegrid .fc-col-header-cell-cushion {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 2px;
}

.fc-timegrid .fc-col-header-cell-cushion div:first-child {
    font-size: 1.1em;
    font-weight: bold;
}

.fc-timegrid .fc-col-header-cell-cushion div:last-child {
    font-size: 0.9em;
    font-weight: normal;
    text-transform: capitalize;
}



.fc-timegrid-divider {
    display: none !important;
}

/* Today indicator for week views - header cell border for both timeGridWeek and timeGridWorkWeek */
.fc-timeGridWeek-view .fc-col-header-cell.fc-day-today,
.fc-timeGridWorkWeek-view .fc-col-header-cell.fc-day-today {
    padding-bottom: 4px !important;
    box-shadow: inset 0 -4px 0 #005075 !important;
}

/* Alternative selector in case the view class is different */
.fc-timegrid .fc-col-header-cell.fc-day-today {
    box-shadow: inset 0 -4px 0 #005075 !important;
}

/* Remove borders between columns in all-day event row */
.fc-timegrid-allday-slot .fc-timegrid-col {
    border-left: none !important;
    border-right: none !important;
}

/* Remove borders from all-day slot area */
.fc-timegrid-allday-slot {
    border-left: none !important;
    border-right: none !important;
}

/* Ensure all-day slot in week/day views has normal height (not affected by month view rules) */
.fc-timeGridWeek-view .fc-timegrid-allday-slot,
.fc-timeGridWorkWeek-view .fc-timegrid-allday-slot,
.fc-timeGridDay-view .fc-timegrid-allday-slot {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Ensure all-day event containers have normal height */
.fc-timeGridWeek-view .fc-timegrid-allday,
.fc-timeGridWorkWeek-view .fc-timegrid-allday,
.fc-timeGridDay-view .fc-timegrid-allday {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Ensure all-day columns have normal height */
.fc-timeGridWeek-view .fc-timegrid-allday .fc-timegrid-col,
.fc-timeGridWorkWeek-view .fc-timegrid-allday .fc-timegrid-col,
.fc-timeGridDay-view .fc-timegrid-allday .fc-timegrid-col {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Remove borders from all-day event containers */
.fc-timegrid-allday .fc-timegrid-col {
    border-left: none !important;
    border-right: none !important;
}

/* Ensure first and last columns don't have borders either */
.fc-timegrid-allday-slot .fc-timegrid-col:first-child,
.fc-timegrid-allday-slot .fc-timegrid-col:last-child {
    border-left: none !important;
    border-right: none !important;
}

/* Make time slots expand to fill available height */
.fc-timegrid-slots {
    height: 100% !important;
}

/* Ensure time slots have flexible height */
.fc-timegrid-slot {
    height: auto !important;
    min-height: 2em !important;
    flex: 1 1 auto !important;
}

/* Specific styles for day view to make hour slots expand */
.fc-timeGridDay-view .fc-timegrid-slot {
    height: auto !important;
    min-height: 3em !important;
    flex: 1 1 auto !important;
}

/* Ensure the timegrid body expands to fill available space */
.fc-timegrid-body {
    height: 100% !important;
    flex: 1 1 auto !important;
}

/* Make the scrollgrid expand properly */
.fc-timegrid .fc-scrollgrid {
    height: 100% !important;
}

/* Ensure the time axis and slots container expand */
.fc-timegrid-axis-chunk,
.fc-timegrid-slots-chunk {
    height: 100% !important;
}

/* Make the main time area expand */
.fc-timegrid-time-area {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure slots table expands */
.fc-timegrid-slots table {
    height: 100% !important;
}

/* More Events Link Styling (+n button) - fits with percentage system */
.fc-more-link,
.fc-daygrid-more-link {
    background-color: #D1D5DB !important;
    color: #222222 !important;
    border: 1px solid #BBBDC0 !important;
    border-radius: 2px !important;
    padding: 2px 4px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: calc(100% - 4px) !important;
    box-sizing: border-box !important;
    text-align: center !important;
    margin: 1px 0 !important;
    cursor: pointer !important;
    height: calc(33.33% - 1px) !important;
    /* Same height as events */
    z-index: 5 !important;
}

.fc-more-link:hover,
.fc-daygrid-more-link:hover {
    background-color: #B8BCC2 !important;
    color: #111111 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Month view specific more link styling */
.fc-dayGridMonth-view .fc-more-link,
.fc-dayGridMonth-view .fc-daygrid-more-link {
    background-color: #D1D5DB !important;
    color: #222222 !important;
    border: 1px solid #BBBDC0 !important;
    border-radius: 2px !important;
    padding: 2px 4px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: calc(100% - 8px) !important;
    box-sizing: border-box !important;
    text-align: center !important;
    margin: 2px 2px !important;
    cursor: pointer !important;
    height: 26px !important;
    /* Same height as events */
    z-index: 10 !important;
    /* Higher z-index to ensure visibility */
    position: relative !important;
    /* Ensure proper positioning */
    visibility: visible !important;
    /* Force visibility */
}

.fc-dayGridMonth-view .fc-more-link:hover,
.fc-dayGridMonth-view .fc-daygrid-more-link:hover {
    background-color: #B8BCC2 !important;
    color: #111111 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Other views keep original styling */
.fc-timeGridWeek-view .fc-more-link,
.fc-timeGridDay-view .fc-more-link,
.fc-timegrid .fc-more-link {
    background-color: #E5E8EB !important;
    color: #333333 !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    text-align: center !important;
    margin: 2px 0 !important;
    cursor: pointer !important;
}

/* Hide FullCalendar popovers since we use custom modal */
.fc-popover,
.fc-more-popover {
    display: none !important;
}

/* Ensure more link works in both day grid and time grid views */
.fc-daygrid .fc-more-link,
.fc-timegrid .fc-more-link {
    background-color: #E5E8EB !important;
    color: #333333 !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    text-align: center !important;
    margin: 2px 0 !important;
    cursor: pointer !important;
}

/* Hide FullCalendar popovers since we use custom modal */
.fc-popover,
.fc-more-popover {
    display: none !important;
}

/* Allow side-by-side events while preventing visual overlap */
.fc-timegrid-event-harness {
    margin: 1px !important;
}

/* Percentage-based event heights for month view - max 2 events + more button */
.fc-dayGridMonth-view .fc-daygrid-event-harness {
    margin-bottom: 2px !important;
    height: 28px !important;
    /* Fixed height to fit 2 events + more button in 95px */
    flex-shrink: 0 !important;
    width: 100% !important;
    overflow: visible !important;
    position: relative !important;
}

/* Allow FullCalendar to hide events beyond dayMaxEvents limit */
.fc-dayGridMonth-view .fc-daygrid-event-harness.fc-daygrid-event-harness-abs {
    display: block !important;
    /* Ensure visible events are shown */
}

/* Ensure FullCalendar's dayMaxEvents logic works properly */
.fc-dayGridMonth-view .fc-daygrid-day-events .fc-daygrid-event-harness:nth-child(n+3):not(.fc-daygrid-more-link) {
    display: none !important;
    /* Hide events beyond the 2nd one, except the more link */
}

/* Make sure the more link is visible when there are hidden events */
.fc-dayGridMonth-view .fc-daygrid-day-events .fc-daygrid-more-link {
    display: flex !important;
    order: 3 !important;
    /* Position as the 3rd item */
}

.fc-dayGridMonth-view .fc-daygrid-event {
    margin-bottom: 0 !important;
    width: calc(100% - 8px) !important;
    position: relative !important;
    height: 26px !important;
    /* Fixed height within harness */
    font-size: 11px !important;
    /* Smaller font */
    line-height: 1.2 !important;
    /* Proportional line height */
    padding: 2px 4px 2px 5px !important;
    /* Slightly more left padding, minimal top adjustment */
    border-radius: 2px !important;
    /* Smaller border radius */
    display: flex !important;
    align-items: flex-start !important;
    /* Align to top but with small padding */
    justify-content: flex-start !important;
    box-sizing: border-box !important;
    max-width: 100% !important;
    overflow: hidden !important;
    margin-left: 2px !important;
    margin-right: 2px !important;
    border-left: 3px solid #ccc !important;
    /* Default 3px left border */
}

/* Style event time in month view */
.fc-dayGridMonth-view .fc-event-time {
    font-family: 'Roboto', sans-serif !important;
    font-weight: 400 !important;
    font-size: 10px !important;
    line-height: 12px !important;
    letter-spacing: 0% !important;
    margin-right: 4px !important;
    display: inline !important;
}

/* Style for event titles in month view */
.fc-dayGridMonth-view .fc-event-title {
    font-family: 'Roboto', sans-serif !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 16px !important;
    letter-spacing: 0% !important;
    white-space: normal !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    padding: 1px 2px 0 4px !important;
    margin: 0 !important;
    flex: 1 !important;
    align-self: flex-start !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

/* Ensure event main content fills proportionally */
.fc-dayGridMonth-view .fc-event-main {
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: flex-start !important;
    width: 100% !important;
    flex: 1 !important;
    padding-top: 1px !important;
}

/* Proportional event main frame */
.fc-dayGridMonth-view .fc-event-main-frame {
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: flex-start !important;
    width: 100% !important;
    flex: 1 !important;
    padding-top: 1px !important;
}

/* Ensure events have proper spacing when side by side */
.fc-timegrid-event {
    box-sizing: border-box !important;
    /* Borders will be set by event type specific rules */
}


/* Month view specific event type styling by background color */
.fc-dayGridMonth-view .fc-daygrid-event[style*="background-color: rgb(255, 224, 209)"],
.fc-dayGridMonth-view .fc-event[style*="background-color: rgb(255, 224, 209)"] {
    border: 1px solid #AD3A00 !important;
    /* Hearing events - full border */
    border-left: 3px solid #AD3A00 !important;
    /* Thicker left border */
}

.fc-dayGridMonth-view .fc-daygrid-event[style*="background-color: rgb(231, 244, 221)"],
.fc-dayGridMonth-view .fc-event[style*="background-color: rgb(231, 244, 221)"] {
    border: 1px solid #53832D !important;
    /* Deadline events - full border */
    border-left: 3px solid #53832D !important;
    /* Thicker left border */
}

.fc-dayGridMonth-view .fc-daygrid-event[style*="background-color: rgb(215, 243, 249)"],
.fc-dayGridMonth-view .fc-event[style*="background-color: rgb(215, 243, 249)"] {
    border: 1px solid #2B8BA1 !important;
    /* Polisweb events - full border */
    border-left: 3px solid #2B8BA1 !important;
    /* Thicker left border */
}

.fc-dayGridMonth-view .fc-daygrid-event[style*="background-color: rgb(245, 245, 245)"],
.fc-dayGridMonth-view .fc-event[style*="background-color: rgb(245, 245, 245)"] {
    border: 1px solid #757575 !important;
    /* Default events - full border */
    border-left: 3px solid #757575 !important;
    /* Thicker left border */
}

/* Fallback: Default borders for all month view events */
.fc-dayGridMonth-view .fc-daygrid-event,
.fc-dayGridMonth-view .fc-event {
    border: 1px solid #757575 !important;
    /* Default gray border - full border */
    border-left: 3px solid #757575 !important;
    /* Thicker left border */
}

/* Full-day events styling - these appear in the all-day row at the top */
.fc-daygrid-event.fc-event-allday,
.fc-event.fc-event-allday {
    height: 28px !important;
    /* Same height as +n more link for consistency */
    border-radius: 4px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 2px 6px !important;
    margin: 1px 0 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* Full-day events in month view */
.fc-dayGridMonth-view .fc-daygrid-event.fc-event-allday,
.fc-dayGridMonth-view .fc-event.fc-event-allday {
    background-color: #f5f5f5 !important;
    border: 1px solid #757575 !important;
    border-left: 3px solid #757575 !important;
    color: #424242 !important;
}

/* Full-day events in week/day views */
.fc-timeGridWeek-view .fc-event.fc-event-allday,
.fc-timeGridWorkWeek-view .fc-event.fc-event-allday,
.fc-timeGridDay-view .fc-event.fc-event-allday {
    background-color: #f5f5f5 !important;
    border: 1px solid #757575 !important;
    border-left: 3px solid #757575 !important;
    color: #424242 !important;
    height: 24px !important;
    /* Slightly smaller in week/day views */
}

/* Full-day event type-specific styling - hearing events */
.fc-daygrid-event.fc-event-allday[data-event-type="hearing"],
.fc-event.fc-event-allday[data-event-type="hearing"] {
    background-color: #fff0e6 !important;
    border: 1px solid #AD3A00 !important;
    border-left: 3px solid #AD3A00 !important;
    color: #521B00 !important;
}

/* Full-day event type-specific styling - deadline events */
.fc-daygrid-event.fc-event-allday[data-event-type="deadline"],
.fc-event.fc-event-allday[data-event-type="deadline"] {
    background-color: #f0f8e6 !important;
    border: 1px solid #53832D !important;
    border-left: 3px solid #53832D !important;
    color: #263D14 !important;
}

/* Full-day event type-specific styling - polisweb events */
.fc-daygrid-event.fc-event-allday[data-event-type="polisweb"],
.fc-event.fc-event-allday[data-event-type="polisweb"] {
    background-color: #e6f7ff !important;
    border: 1px solid #2B8BA1 !important;
    border-left: 3px solid #2B8BA1 !important;
    color: #0A3C47 !important;
}

/* Removed data-attribute based rules - using background color approach instead */

/* Weekly, Work Week, and Daily view styling using same approach as monthly view */
.fc-timeGridWeek-view .fc-event[style*="background-color: rgb(255, 224, 209)"],
.fc-timeGridWorkWeek-view .fc-event[style*="background-color: rgb(255, 224, 209)"],
.fc-timeGridDay-view .fc-event[style*="background-color: rgb(255, 224, 209)"] {
    border: 1px solid #AD3A00 !important;
    /* Hearing events - full border */
    border-left: 3px solid #AD3A00 !important;
    /* Thicker left border */
}

.fc-timeGridWeek-view .fc-event[style*="background-color: rgb(231, 244, 221)"],
.fc-timeGridWorkWeek-view .fc-event[style*="background-color: rgb(231, 244, 221)"],
.fc-timeGridDay-view .fc-event[style*="background-color: rgb(231, 244, 221)"] {
    border: 1px solid #53832D !important;
    /* Deadline events - full border */
    border-left: 3px solid #53832D !important;
    /* Thicker left border */
}

.fc-timeGridWeek-view .fc-event[style*="background-color: rgb(215, 243, 249)"],
.fc-timeGridWorkWeek-view .fc-event[style*="background-color: rgb(215, 243, 249)"],
.fc-timeGridDay-view .fc-event[style*="background-color: rgb(215, 243, 249)"] {
    border: 1px solid #2B8BA1 !important;
    /* Polisweb events - full border */
    border-left: 3px solid #2B8BA1 !important;
    /* Thicker left border */
}

.fc-timeGridWeek-view .fc-event[style*="background-color: rgb(245, 245, 245)"],
.fc-timeGridWorkWeek-view .fc-event[style*="background-color: rgb(245, 245, 245)"],
.fc-timeGridDay-view .fc-event[style*="background-color: rgb(245, 245, 245)"] {
    border: 1px solid #757575 !important;
    /* Default events - full border */
    border-left: 3px solid #757575 !important;
    /* Thicker left border */
}

/* Default border for all weekly/work week/daily view events as fallback */
.fc-timeGridWeek-view .fc-event,
.fc-timeGridWorkWeek-view .fc-event,
.fc-timeGridDay-view .fc-event {
    border: 1px solid #757575 !important;
    /* Default gray border - full border */
    border-left: 3px solid #757575 !important;
    /* Thicker left border */
}

/* Clean up - removed redundant rules */

.fc-timeGridWeek-view .fc-event-title,
.fc-timeGridWorkWeek-view .fc-event-title,
.fc-timeGridDay-view .fc-event-title,
.fc-timegrid .fc-event-title {
    font-family: 'Roboto', sans-serif !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 16px !important;
    letter-spacing: 0% !important;
    padding-left: 8px !important;
}

.fc-timeGridWeek-view .fc-event-time,
.fc-timeGridWorkWeek-view .fc-event-time,
.fc-timeGridDay-view .fc-event-time,
.fc-timegrid .fc-event-time {
    display: none !important;
}

/* Fix event height to match duration - prevent content from extending beyond time bounds */
.fc-timegrid-event {
    overflow: hidden !important;
}

.fc-timegrid-event .fc-event-main {
    height: 100% !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

.fc-timegrid-event .fc-event-main-frame {
    height: 100% !important;
    overflow: hidden !important;
    flex: 1 !important;
}

.fc-timegrid-event .fc-event-title-container {
    height: 100% !important;
    overflow: hidden !important;
    flex: 1 !important;
}

/* Ensure custom event content doesn't exceed event bounds */
.fc-timegrid-event .fc-event-title {
    height: 100% !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Force event content to stay within bounds */
.fc-timegrid-event-harness {
    overflow: hidden !important;
}

.fc-timegrid-event-harness .fc-event {
    height: 100% !important;
    overflow: hidden !important;
}

.fc-important-marker {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-top: 15px solid red;
    border-right: 15px solid transparent;
    z-index: 20;
}

.fc-daygrid-day {
    position: relative;
}

/* Header triangle marker */
.fc-day-header-content {
    position: relative;
    padding: 4px;
    min-height: 40px;
}

.important-marker-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-top: 12px solid red;
    border-right: 12px solid transparent;
    z-index: 1;
}

/* Event styling */
.custom-event-container {
    position: relative;
    padding: 4px;
    margin: 2px 0;
}

.important-event {
    padding-bottom: 20px; /* Space for "Importante" label */
}

.important-marker-event {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    border-top: 12px solid red;
    border-right: 12px solid transparent;
}

.important-label {
    position: absolute;
    bottom: 2px;
    left: 4px;
    font-size: 11px;
    color: red;
}

/* Month view specific styles */
.fc-dayGridMonth-view .fc-daygrid-day-top {
    position: relative;
}

.fc-dayGridMonth-view .important-marker-header {
    border-top-width: 15px;
    border-right-width: 15px;
}

/* Right panel styles */
.day-events-panel .important-event {
    border-left: 3px solid red;
}

.day-events-panel .important-label {
    position: static;
    margin-top: 4px;
}

.fc-timegrid-now-indicator-line {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
  }