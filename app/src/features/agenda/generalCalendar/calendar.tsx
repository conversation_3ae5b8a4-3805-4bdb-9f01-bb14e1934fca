import "./styles/fullCalendar.css";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import itLocale from "@fullcalendar/core/locales/it";
import { useUser } from "../../../store/UserStore";
import { CustomEventUI } from "./helpers/customUIEvents";
import CalendarNavigationButtons from "./helpers/CalendarNavigationButtons";
import CalendarActionButtons from "./helpers/CalendarActionButtons";
import { ICalendarProps, ICalendarEvent } from "./typings/generalCalendar.interface";
import { useHandleEventDrop } from "./hooks/useHandleEventDrop";
import { useNavigate, useLocation } from "react-router-dom";
import { manageDeadlinesNotice, manageHearingsNotice } from "./utils";
import { gettingCalendarView, gettingCalendarViewName } from "./helpers/gettingCalendarViewName";
import { useEffect, useState } from "react";
import { Grid } from "@mui/material";
import moment from "moment";
import Filters from "./filters";
import DayEvents from "./components/DayEvents";
import MoreEventsModal from "./components/MoreEventsModal";
import QuickImpegnoModal from "./components/QuickImpegnoModal";
import { useDoubleClick } from "./hooks/useDoubleClick";
import ToastNotification from "../../../custom-components/ToastNotification";
import { Button, IconButton } from "@vapor/react-material";
import { Close } from "@mui/icons-material";
import { CalendarDataProvider } from "./addCalendar/impegno/context/CalendarDataContext";
import NewImpegnoIndexModal from "./addCalendar/impegno/newIndex";
import Activities from "./components/Activities";

const parseQueryDate = (dateValue: number) => {
    if (!dateValue || dateValue === 0) return moment();
    if (dateValue > 1000000000000) {
        return moment(dateValue);
    } else {
        return moment.unix(dateValue);
    }
};

function addEndTimeToEvents(events: ICalendarEvent[]): any[] {
    if (!events || !Array.isArray(events)) {
        return [];
    }

    const getEventTypeColor = (type: string): { box: string; text: string; background: string } => {
        switch (type) {
            case "hearing":
                return {
                    box: "#AD3A00",
                    text: "#521B00",
                    background: "#FFE0D1"
                };
            case "deadline":
                return {
                    box: "#53832D",
                    text: "#263D14",
                    background: "#E7F4DD"
                };
            case "polisweb":
                return {
                    box: "#2B8BA1",
                    text: "#0A3C47",
                    background: "#D7F3F9"
                };
            default:
                return {
                    box: "#757575",
                    text: "#424242",
                    background: "#F5F5F5"
                };
        }
    };

    return events.map((event: ICalendarEvent) => {
        let eventWithColors: any = { ...event };

        // Apply colors to all events (including full-day events)
        const colors = getEventTypeColor(event.type);
        eventWithColors.backgroundColor = colors.background;
        eventWithColors.borderColor = colors.box;

        // Handle full-day events
        if (event.full_day === "1") {
            return {
                ...eventWithColors,
                allDay: true,
                classNames: ['fc-event-allday'],
                extendedProps: {
                    ...eventWithColors.extendedProps,
                    eventType: event.type,
                    isFullDay: true
                }
            };
        }

        if (!(event as any).end && event.start) {
            const startDate = new Date(event.start);
            const endDate = new Date(startDate.getTime() + Number(event.durata) * 60000);
            return {
                ...eventWithColors,
                start: event.start, // Keep original start format
                end: endDate
            };
        }

        return eventWithColors;
    });
}

const validateEvents = (events: ICalendarEvent[]): ICalendarEvent[] => {
    if (!Array.isArray(events)) return [];

    return events.filter((event) => {
        if (!event || typeof event !== "object") return false;
        
        // Skip validation for full-day events
        if (event.full_day === "1") return true;
        
        if (!event.start) return false;
        try {
            const startDate = new Date(event.start);
            if (isNaN(startDate.getTime())) return false;
        } catch (error) {
            return false;
        }
        if ((event as any).end) {
            try {
                const endDate = new Date((event as any).end);
                if (isNaN(endDate.getTime())) {
                    delete (event as any).end;
                }
            } catch (error) {
                delete (event as any).end;
            }
        }

        return true;
    });
};

const processEventData = (eventData: ICalendarEvent[]): any[] => {
    console.log("🔍 DEBUG: Raw eventData received:", eventData);
    
    const safeEventData = validateEvents(eventData);
    console.log("🔍 DEBUG: After validation:", safeEventData);
    
    let updatedEvents = manageDeadlinesNotice(safeEventData);
    updatedEvents = manageHearingsNotice(updatedEvents);
    updatedEvents = addEndTimeToEvents(updatedEvents);
    
    console.log("🔍 DEBUG: After processing:", updatedEvents);
    
    // Log any events that have full_day or allDay properties
    updatedEvents.forEach((event, index) => {
        if (event.full_day === "1" || event.allDay) {
            console.log(`🔍 DEBUG: Full day event found at index ${index}:`, {
                title: event.title,
                full_day: event.full_day,
                allDay: event.allDay,
                start: event.start,
                giornata_intera: event.giornata_intera
            });
        }
    });

    return updatedEvents;
};

const getValidatedEventsForCalendar = (updatedEvents: any[]): any[] => {
    try {
        const validatedEvents = Array.isArray(updatedEvents) ? updatedEvents : [];
        return validatedEvents.filter((event) => {
            return event && event.start && !isNaN(new Date(event.start).getTime());
        });
    } catch (error) {
        return [];
    }
};

const sortEventsForDisplay = (events: any[], currentView: string): any[] => {
    // For monthly view, prioritize full-day events to show them first
    if (currentView === "month" || currentView === "dayGridMonth") {
        return [...events].sort((a, b) => {
            // Full-day events first
            if (a.allDay && !b.allDay) return -1;
            if (!a.allDay && b.allDay) return 1;

            // If both are full-day or both are not full-day, sort by start time
            const aStart = new Date(a.start).getTime();
            const bStart = new Date(b.start).getTime();
            return aStart - bStart;
        });
    }

    // For other views, keep original order or sort by start time
    return [...events].sort((a, b) => {
        const aStart = new Date(a.start).getTime();
        const bStart = new Date(b.start).getTime();
        return aStart - bStart;
    });
};

const getInitialDate = (query: { date?: number }) => {
    try {
        if (query.date && query.date !== 0) {
            const targetDate = parseQueryDate(query.date);
            return targetDate.isValid() ? targetDate.toDate() : moment().toDate();
        }
        return moment().toDate();
    } catch (error) {
        return moment().toDate();
    }
};

const getCalendarViews = () => {
    return {
        dayGridMonth: {
            dayHeaderFormat: { weekday: "long" as const },
            dayMaxEvents: 2
        },
        timeGridWeek: {
            dayHeaderContent: createDayHeaderContent,
            hiddenDays: [],
            dayMaxEvents: 1,
            nowIndicator: true
        },
        timeGridWorkWeek: {
            type: "timeGridWeek" as const,
            hiddenDays: [0, 6],
            dayHeaderContent: createDayHeaderContent,
            dayMaxEvents: 1,
            nowIndicator: true
        },
        timeGridDay: {
            dayHeaderContent: createDayHeaderContent,
            dayMaxEvents: 2,
            nowIndicator: true
        }
    };
};

const createDayHeaderContent = (arg: any) => {
    return (
        <>
            <div>{arg.date.getDate()}</div>
            <div>{arg.date.toLocaleDateString("it-IT", { weekday: "long" })}</div>
        </>
    );
};

const createViewDidMountHandler = (calendarRef: React.MutableRefObject<FullCalendar | null>, query: { viewName: string }, setQuery: React.Dispatch<React.SetStateAction<any>>) => {
    return function () {
        try {
            const calendarAPI = calendarRef.current?.getApi();
            if (calendarAPI && calendarAPI.view) {
                const viewName = calendarAPI.view.type;
                const internalViewName = gettingCalendarViewName(viewName);
                if (query.viewName !== "timeGridWorkWeek" && query.viewName !== internalViewName) {
                    setQuery((prevQuery: any) => ({
                        ...prevQuery,
                        viewName: internalViewName
                    }));
                }
            }
        } catch (error) {
            console.error("Error in viewDidMount:", error);
        }
    };
};

const filterEventsByDate = (events: any[], selectedDate: Date | null) => {
    if (!selectedDate || !Array.isArray(events)) {
        return [];
    }

    return events.filter((event: any) => {
        if (!event || !event.start) return false;

        try {
            const eventDate = new Date(event.start);
            return eventDate.getDate() === selectedDate.getDate() && eventDate.getMonth() === selectedDate.getMonth() && eventDate.getFullYear() === selectedDate.getFullYear();
        } catch (error) {
            return false;
        }
    });
};

const addImportantEventMarkers = (updatedEvents: any[]) => {
    // Wait for the calendar to render
    setTimeout(() => {
        try {
            // Only add markers in month view
            const calendarElement = document.querySelector(".fc-dayGridMonth-view");
            if (!calendarElement) return;

            // Remove existing markers first
            const existingMarkers = document.querySelectorAll(".fc-important-marker");
            existingMarkers.forEach((marker) => marker.remove());

            // Get all day cells
            const dayCells = document.querySelectorAll(".fc-daygrid-day");

            dayCells.forEach((dayCell) => {
                const dateAttr = dayCell.getAttribute("data-date");
                if (!dateAttr) return;

                const cellDate = new Date(dateAttr);

                // Find events for this date
                const eventsForThisDate = updatedEvents.filter((event) => {
                    if (!event.start) return false;

                    try {
                        const eventDate = new Date(event.start);
                        return eventDate.getFullYear() === cellDate.getFullYear() && eventDate.getMonth() === cellDate.getMonth() && eventDate.getDate() === cellDate.getDate();
                    } catch (error) {
                        return false;
                    }
                });

                const hasImportantEvent = eventsForThisDate.some((event) => event.important === "1");

                if (hasImportantEvent) {
                    const marker = document.createElement("div");
                    marker.classList.add("fc-important-marker");
                    marker.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 0;
                        height: 0;
                        border-top: 15px solid red;
                        border-right: 15px solid transparent;
                        z-index: 10;
                        pointer-events: none;
                    `;

                    // Ensure the day cell has relative positioning
                    (dayCell as HTMLElement).style.position = "relative";
                    dayCell.appendChild(marker);
                }
            });
        } catch (error) {
            console.error("Error adding important event markers:", error);
        }
    }, 100);
};

const hasImportantEvents = (date: Date, events: any[]): boolean => {
    // Create date string in YYYY-MM-DD format without timezone conversion
    const targetDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;

    return events.some((event) => {
        if (!event.start || !event.important) return false;

        // Extract just the date part from event.start
        const eventDate = event.start.split(" ")[0];
        return eventDate === targetDate && event.important === "1";
    });
};

export default function Calendar(props: ICalendarProps) {
    const { query, setQuery, calendarRef, fetchEventData, eventData, eventResponse, DEFAULT_QUERY, monthTitle, setMonthTitle, items, t, calendarData } = props;
    const [rightPanelType, setRightPanelType] = useState<string>("activities");
    const { modules }: any = useUser();
    const { handleEventDrop } = useHandleEventDrop(fetchEventData, query);
    const navigate = useNavigate();
    const location = useLocation();
    const loggedUserCampiAgenda = modules.campi_agenda !== undefined ? JSON.parse(modules.campi_agenda) : null;

    const impegniMultiutente = modules.provisioningRow?.impegni_multiutente || null;

    const netlexSettingsFileId = modules.netlexSettings?.file_id || null;

    const [rightPanelOpen, setRightPanelOpen] = useState(false);
    const [leftPanelOpen, setLeftPanelOpen] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [moreEventsModalOpen, setMoreEventsModalOpen] = useState(false);
    const [moreEventsData, setMoreEventsData] = useState<{
        events: any[];
        date: Date | null;
    }>({ events: [], date: null });

    const [openImpengoModal, setOpenImpegnoModal] = useState<boolean>(false);
    const [openQuickImpegno, setOpenQuickImpegno] = useState({ state: false, view: "" });
    const [showNotification, setShowNotification] = useState({
        show: false,
        wasOpen: false
    });
    const [savedFormData, setSavedFormData] = useState<any>(null);

    const handleQuickImpegnoModal = (_event: any, reason: any, formData?: any) => {
        if (reason === "backdropClick") {
            setSavedFormData(formData);
            setShowNotification({ show: true, wasOpen: true });
        }
        setOpenQuickImpegno({ state: false, view: "" });
    };

    const handleAnnullaButton = () => {
        setShowNotification({ show: false, wasOpen: true });
        setOpenQuickImpegno((prev: any) => ({ ...prev, state: true }));
    };

    const handleFormDataChange = (formData: any) => {
        setSavedFormData(formData);
    };

    const handleProperQuickImpegnoClose = () => {
        setOpenQuickImpegno({ state: false, view: "" });
        setShowNotification({ wasOpen: false, show: false });
        setSavedFormData(null);
    };

    const closeNotificationWithX = () => {
        setShowNotification({ show: false, wasOpen: false });
        setSavedFormData(null);
        sessionStorage.removeItem("giornataSwitchImpegno");
    };

    const handleEventResize = (info: any) => handleEventDrop(info);

    const [showAddForm, setShowAddForm] = useState(false);

    let calendarAPI: any = calendarRef?.current?.getApi();
    const updatedEvents = processEventData(eventData);
    const sortedEvents = sortEventsForDisplay(updatedEvents, calendarAPI?.currentData.currentViewType);

    useEffect(() => {
        try {
            if (calendarAPI && query.date && query.date !== 0) {
                const targetDate = parseQueryDate(query.date);
                if (targetDate.isValid()) {
                    calendarAPI.gotoDate(targetDate.toDate());
                } else {
                    console.warn("Invalid target date:", query.date);
                }
            }
        } catch (error) {
            console.error("Error navigating to date:", error);
        }
    }, [query.date]);

    useEffect(() => {
        try {
            if (calendarAPI && query.date && query.date !== 0) {
                const targetDate = parseQueryDate(query.date);

                if (targetDate.isValid()) {
                    calendarAPI.gotoDate(targetDate.toDate());
                } else {
                    console.warn("Invalid target date:", query.date);
                }
            }
        } catch (error) {
            console.error("Error navigating to date on calendar ref change:", error);
        }
    }, [calendarRef.current]);

    const handleSingleClick = (arg: any) => {
        setRightPanelType("dayEvents");
        if (selectedDate && selectedDate.getDate() === arg.date.getDate() && selectedDate.getMonth() === arg.date.getMonth() && selectedDate.getFullYear() === arg.date.getFullYear()) {
            setRightPanelOpen(!rightPanelOpen);
        } else {
            setSelectedDate(arg.date);
            setRightPanelOpen(true);
        }
    };

    const handleDoubleClick = (arg: any) => {
        setOpenQuickImpegno({ state: true, view: arg.view.type });
        setSelectedDate(arg.date);
    };

    const handleDateClick = useDoubleClick(
        handleSingleClick,
        handleDoubleClick,
        300 // 300ms delay
    );

    useEffect(() => {
        const handleMoreLinkClick = (event: Event) => {
            event.preventDefault();
            event.stopPropagation();

            const target = event.target as HTMLElement;
            const dayCell = target.closest(".fc-daygrid-day");

            if (dayCell) {
                // Get the date from the day cell
                const dateAttr = dayCell.getAttribute("data-date");
                const clickDate = dateAttr ? new Date(dateAttr) : new Date();

                const eventsForDate = sortedEvents.filter((event: any) => {
                    if (!event.start) return false;
                    const eventDate = new Date(event.start);
                    return eventDate.getDate() === clickDate.getDate() && eventDate.getMonth() === clickDate.getMonth() && eventDate.getFullYear() === clickDate.getFullYear();
                });

                setMoreEventsData({
                    events: eventsForDate,
                    date: clickDate
                });
                setMoreEventsModalOpen(true);
            }
        };

        const attachListeners = () => {
            const moreLinks = document.querySelectorAll(".fc-more-link");
            moreLinks.forEach((link) => {
                link.removeEventListener("click", handleMoreLinkClick);

                link.addEventListener("click", handleMoreLinkClick, true);
            });
        };

        const timer = setTimeout(attachListeners, 200);

        const observer = new MutationObserver(() => {
            setTimeout(attachListeners, 100);
        });

        const calendarElement = document.querySelector(".fc");
        if (calendarElement) {
            observer.observe(calendarElement, {
                childList: true,
                subtree: true,
                attributes: false
            });
        }

        return () => {
            clearTimeout(timer);
            observer.disconnect();
        };
    }, [sortedEvents, query.viewName]);

    // Add effect to handle markers when events or view changes
    useEffect(() => {
        if (query.viewName === "month" || query.viewName === "dayGridMonth") {
            addImportantEventMarkers(updatedEvents);
        }
    }, [updatedEvents, query.viewName]);

    // Add CSS styles for the calendar icons
    // This is done to ensure the icons are styled correctly and positioned
    // This useEffect runs once to inject the styles into the document head
    useEffect(() => {
        const style = document.createElement("style");
        style.textContent = `
            .calendar-day-icon,
            .calendar-header-icon {
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 0;
                border-top: 15px solid red;
                border-right: 15px solid transparent;
                z-index: 1;
            }
            .fc-daygrid-day-frame {
                position: relative !important;
            }
            .fc-scrollgrid-sync-inner {
                position: relative !important;
            }
        `;
        document.head.appendChild(style);
        return () => style.remove();
    }, []);

    // this useeffect is used to add markers to the calendar header and day cells
    // after the calendar has rendered and the events have been processed
    // It checks if the current view is month or dayGridMonth and adds markers accordingly
    // It also handles the case for non-month views by manually triggering header markers
    useEffect(() => {
        // Give the calendar a moment to render
        const timer = setTimeout(() => {
            // Apply markers for both month view and other views
            if (query.viewName === "month" || query.viewName === "dayGridMonth") {
                addImportantEventMarkers(updatedEvents);
            } else {
                // For non-month views, manually trigger header markers
                const headers = document.querySelectorAll(".fc-col-header-cell");
                headers.forEach((header) => {
                    const dateAttr = header.getAttribute("data-date");
                    if (dateAttr) {
                        const date = new Date(dateAttr);
                        if (hasImportantEvents(date, sortedEvents)) {
                            const iconEl = document.createElement("div");
                            iconEl.className = "calendar-header-icon";
                            const innerCell = header.querySelector(".fc-scrollgrid-sync-inner");
                            if (innerCell) {
                                innerCell.insertBefore(iconEl, innerCell.firstChild);
                            }
                        }
                    }
                });
            }
        }, 200); // Increased timeout to ensure calendar is fully rendered

        return () => clearTimeout(timer);
    }, [eventData, query.viewName]); // Add eventData as dependency to react to data changes

    return (
        <>
            <CalendarNavigationButtons
                query={query}
                setQuery={setQuery}
                calendarRef={calendarRef}
                fetchEventData={fetchEventData}
                DEFAULT_QUERY={DEFAULT_QUERY}
                monthTitle={monthTitle}
                setMonthTitle={setMonthTitle}
                t={t}
                leftPanelOpen={leftPanelOpen}
                setLeftPanelOpen={setLeftPanelOpen}
                rightPanelOpen={rightPanelOpen}
                setRightPanelOpen={setRightPanelOpen}
                selectedView={query.viewName}
                onViewChange={(viewName: string) => {
                    setQuery({ ...query, viewName });
                }}
                setRightPanelType={setRightPanelType}
                rightPanelType={rightPanelType}
            >
                <CalendarActionButtons
                    query={query}
                    setQuery={setQuery}
                    calendarRef={calendarRef}
                    fetchEventData={fetchEventData}
                    DEFAULT_QUERY={DEFAULT_QUERY}
                    setMonthTitle={setMonthTitle}
                    t={t}
                    leftPanelOpen={leftPanelOpen}
                    setLeftPanelOpen={setLeftPanelOpen}
                    rightPanelOpen={rightPanelOpen}
                    setRightPanelOpen={setRightPanelOpen}
                    selectedView={query.viewName}
                    onViewChange={(viewName: string) => {
                        setQuery({ ...query, viewName });
                    }}
                    calendarData={calendarData}
                    eventData={eventData}
                    eventResponse={eventResponse}
                    openImpegnoModal={() => setOpenImpegnoModal(true)}
                    setRightPanelType={setRightPanelType}
                    rightPanelType={rightPanelType}
                    showAddForm={showAddForm}
                    setShowAddForm={setShowAddForm}
                />
            </CalendarNavigationButtons>

            <Grid container spacing={3}>
                {/* Left Panel */}
                {leftPanelOpen && (
                    <Grid item md={leftPanelOpen ? 3 : 0} sx={{ transition: "all 0.3s ease", overflow: "hidden" }}>
                        <Filters
                            DEFAULT_QUERY={DEFAULT_QUERY}
                            query={query}
                            setQuery={setQuery}
                            fetchEventData={fetchEventData}
                            calendarData={calendarData}
                            calendarRef={calendarRef}
                            setMonthTitle={setMonthTitle}
                            t={t}
                        />
                    </Grid>
                )}

                {/* Middle Panel */}
                <Grid item md={leftPanelOpen && rightPanelOpen ? 6 : leftPanelOpen || rightPanelOpen ? 9 : 12}>
                    <FullCalendar
                        initialView={gettingCalendarView(query.viewName)}
                        initialDate={getInitialDate(query)}
                        plugins={[dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin]}
                        timeZone="local"
                        headerToolbar={false}
                        handleWindowResize={true}
                        height="76vh"
                        expandRows={true}
                        stickyHeaderDates={true}
                        allDaySlot={true}
                        allDayText=""
                        slotMinTime="08:00:00"
                        slotMaxTime="20:00:00"
                        nowIndicator={true}
                        slotLabelFormat={{
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: false
                        }}
                        dayHeaderFormat={{ weekday: "long", day: "numeric" }}
                        ref={calendarRef}
                        locales={[itLocale]}
                        locale="it"
                        dateClick={handleDateClick}
                        events={getValidatedEventsForCalendar(sortedEvents)}
                        eventDrop={handleEventDrop}
                        eventResize={handleEventResize}
                        slotDuration="00:30:00"
                        eventDisplay="block"
                        fixedWeekCount={false}
                        showNonCurrentDates={true}
                        aspectRatio={1.35}
                        slotEventOverlap={false}
                        eventOverlap={false}
                        moreLinkClick={(info) => {
                            // Get the date from the clicked more link
                            const clickDate = info.date;

                            // Get all events for this date from the processed events
                            const eventsForDate = sortedEvents.filter((event: any) => {
                                if (!event.start) return false;
                                const eventDate = new Date(event.start);
                                return eventDate.getDate() === clickDate.getDate() && eventDate.getMonth() === clickDate.getMonth() && eventDate.getFullYear() === clickDate.getFullYear();
                            });

                            setMoreEventsData({
                                events: eventsForDate,
                                date: clickDate
                            });
                            setMoreEventsModalOpen(true);

                            return ""; // Prevent default popover
                        }}
                        moreLinkContent={(args) => {
                            return `+${args.num}`;
                        }}
                        views={getCalendarViews()}
                        displayEventTime={false}
                        displayEventEnd={false}
                        eventContent={(event: any) =>
                            CustomEventUI({
                                event,
                                items,
                                query,
                                loggedUserCampiAgenda,
                                impegniMultiutente,
                                netlexSettingsFileId,
                                navigate,
                                location,
                                t
                            })
                        }
                        datesSet={() => {
                            if (query.viewName === "month" || query.viewName === "dayGridMonth") {
                                addImportantEventMarkers(updatedEvents);
                            }
                        }}
                        viewDidMount={createViewDidMountHandler(calendarRef, query, setQuery)}
                        dayCellDidMount={(arg) => {
                            if (arg.view.type === "dayGridMonth" && hasImportantEvents(arg.date, sortedEvents)) {
                                const iconEl = document.createElement("div");
                                iconEl.className = "calendar-day-icon";
                                const dayFrame = arg.el.querySelector(".fc-daygrid-day-frame");
                                if (dayFrame) {
                                    dayFrame.insertBefore(iconEl, dayFrame.firstChild);
                                }
                            }
                        }}
                        dayHeaderDidMount={(arg) => {
                            if (arg.view.type !== "dayGridMonth" && hasImportantEvents(arg.date, sortedEvents)) {
                                const iconEl = document.createElement("div");
                                iconEl.className = "calendar-header-icon";
                                const innerCell = arg.el.querySelector(".fc-scrollgrid-sync-inner");
                                if (innerCell) {
                                    innerCell.insertBefore(iconEl, innerCell.firstChild);
                                }
                            }
                        }}
                        eventDidMount={(info) => {
                            // Add data attributes for styling full-day events
                            if (info.event.allDay && info.event.extendedProps?.isFullDay) {
                                info.el.setAttribute('data-event-type', info.event.extendedProps.eventType || 'default');
                                info.el.classList.add('fc-event-allday');
                            }
                        }}
                    />
                </Grid>

                {/* Right Panel */}
                {rightPanelOpen && (
                    <Grid item md={rightPanelOpen ? 3 : 0} sx={{ transition: "all 0.3s ease" }}>
                        {rightPanelType === "activities" ? (
                            <Activities
                                setSavedFormData={setSavedFormData}
                                setOpenQuickImpegno={setOpenQuickImpegno}
                                setRightPanelOpen={setRightPanelOpen}
                                t={t}
                                setShowAddForm={setShowAddForm}
                                showAddForm={showAddForm}
                            />
                        ) : (
                            <DayEvents selectedDate={selectedDate} events={filterEventsByDate(sortedEvents, selectedDate)} setRightPanelOpen={setRightPanelOpen} t={t} />
                        )}
                    </Grid>
                )}
            </Grid>

            {/* More Events Modal */}
            <MoreEventsModal
                open={moreEventsModalOpen}
                onClose={() => setMoreEventsModalOpen(false)}
                events={moreEventsData.events}
                date={moreEventsData.date}
                query={query}
                loggedUserCampiAgenda={loggedUserCampiAgenda}
                impegniMultiutente={impegniMultiutente}
                netlexSettingsFileId={netlexSettingsFileId}
            />

            {openQuickImpegno.state && (
                <QuickImpegnoModal
                    open={openQuickImpegno.state}
                    view={openQuickImpegno.view}
                    wasOpen={showNotification.wasOpen}
                    selectedDate={selectedDate}
                    handleModal={handleQuickImpegnoModal}
                    savedFormData={savedFormData}
                    onFormDataChange={handleFormDataChange}
                    handleProperClose={handleProperQuickImpegnoClose}
                    fetchEventData={
                        fetchEventData
                            ? async (query?: any) => {
                                  fetchEventData(query);
                              }
                            : async (_query?: any) => {}
                    }
                    query={query}
                    setOpenImpegnoModal={setOpenImpegnoModal}
                />
            )}

            <ToastNotification
                severity="warning"
                showNotification={showNotification.show}
                setShowNotification={() => setShowNotification({ ...showNotification, show: true })}
                title={t("Modifiche rimosse")}
                action={
                    <IconButton size="small" onClick={closeNotificationWithX}>
                        <Close />
                    </IconButton>
                }
                text={t("L’impegno è stato chiuso e non verrà creato. Vuoi annullare l’operazione?")}
                customButtons={[
                    <Button variant="contained" sx={{ mt: 1, ml: 10 }} size="small" onClick={handleAnnullaButton}>
                        {t("Annulla")}
                    </Button>
                ]}
            />
            {openImpengoModal && (
                <CalendarDataProvider>
                    <NewImpegnoIndexModal
                        open={openImpengoModal}
                        onClose={() => setOpenImpegnoModal(false)}
                        fetchEventData={
                            fetchEventData
                                ? async (query?: any) => {
                                      fetchEventData(query);
                                  }
                                : async (_query?: any) => {}
                        }
                        query={query}
                    />
                </CalendarDataProvider>
            )}
        </>
    );
}

